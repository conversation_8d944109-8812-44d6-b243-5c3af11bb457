// extremeSearch(researchPrompt)
// --> Plan research using LLM to generate a structured research plan
// ----> Break research into components with discrete search queries
// ----> For each search query, search web and collect sources
// ----> Use structured source collection to provide comprehensive research results
// ----> Return all collected sources and research data to the user

import Exa from 'exa-js';
import { type DataStreamWriter, generateObject, generateText, tool } from 'ai';
import { z } from 'zod';
import { serverEnv } from '@/env/server';
import { myProvider } from '@/lib/ai/providers';

// Code execution is currently disabled - can be enabled with Daytona integration later

export const exa = new Exa(serverEnv.EXA_API_KEY);

type SearchResult = {
  title: string;
  url: string;
  content: string;
  publishedDate: string;
  favicon: string;
};

// Interface for Exa API search result
interface ExaSearchResult {
  title?: string;
  url: string;
  text?: string;
  publishedDate?: string;
  favicon?: string;
}

export type Research = {
  text: string;
  toolResults: any[];
  sources: SearchResult[];
  charts: any[];
};

enum SearchCategory {
  NEWS = 'news',
  COMPANY = 'company',
  RESEARCH_PAPER = 'research paper',
  GITHUB = 'github',
  FINANCIAL_REPORT = 'financial report',
}

const searchWeb = async (query: string, category?: SearchCategory) => {
  console.log(`searchWeb called with query: "${query}", category: ${category}`);

  if (!query || query.trim().length === 0) {
    console.log('Empty query provided to searchWeb');
    return [];
  }

  if (!serverEnv.EXA_API_KEY) {
    console.error('EXA_API_KEY is not configured');
    return [];
  }

  try {
    // Add timeout to prevent hanging
    const searchPromise = exa.searchAndContents(query.trim(), {
      numResults: 5,
      type: 'keyword',
      ...(category
        ? {
            category: category as SearchCategory,
          }
        : {}),
    });

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () => reject(new Error('Search timeout after 30 seconds')),
        30000,
      );
    });

    const { results } = (await Promise.race([
      searchPromise,
      timeoutPromise,
    ])) as { results: ExaSearchResult[] };

    console.log(
      `searchWeb received ${results?.length || 0} results from Exa API`,
    );

    const mappedResults = (results || [])
      .filter((r: ExaSearchResult) => r?.url) // Only include results with valid URLs
      .map((r: ExaSearchResult) => ({
        title: r.title || 'Untitled',
        url: r.url,
        content: r.text || '',
        publishedDate: r.publishedDate || '',
        favicon: r.favicon || '',
      })) as SearchResult[];

    console.log(`searchWeb returning ${mappedResults.length} results`);
    return mappedResults;
  } catch (error) {
    console.error('Error in searchWeb:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      query: query,
      category: category,
    });
    return [];
  }
};

const getContents = async (links: string[]) => {
  console.log(`getContents called with ${links.length} URLs:`, links);

  if (!serverEnv.EXA_API_KEY) {
    console.error('EXA_API_KEY is not configured for getContents');
    return [];
  }

  // Filter out invalid URLs and limit to 5 URLs max to avoid API limits
  const validLinks = links
    .filter((url) => url && typeof url === 'string' && url.startsWith('http'))
    .slice(0, 5);

  if (validLinks.length === 0) {
    console.log('No valid URLs to fetch content for');
    return [];
  }

  console.log(
    `Fetching content for ${validLinks.length} valid URLs:`,
    validLinks,
  );

  try {
    console.log('Starting Exa API getContents call...');

    // Add timeout to prevent hanging
    const contentPromise = exa.getContents(validLinks, {
      text: {
        maxCharacters: 3000,
        includeHtmlTags: false,
      },
      livecrawl: 'preferred',
    });

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () => reject(new Error('Content retrieval timeout after 20 seconds')),
        20000,
      );
    });

    console.log('Waiting for Exa API response...');
    const result = (await Promise.race([contentPromise, timeoutPromise])) as {
      results: ExaSearchResult[];
    };

    console.log('Exa API response received successfully');

    console.log(
      `getContents received ${result.results.length} results from Exa API`,
    );

    const mappedResults =
      result.results
        ?.filter((r: ExaSearchResult) => r?.text) // Only include results with actual content
        ?.map((r: ExaSearchResult) => ({
          title: r.title || 'Untitled',
          url: r.url,
          content: r.text || '',
          publishedDate: r.publishedDate || '',
          favicon: r.favicon || '',
        })) || [];

    console.log(`getContents returning ${mappedResults.length} mapped results`);
    return mappedResults;
  } catch (error) {
    console.error('Error in getContents:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      urls: validLinks,
    });
    return [];
  }
};

const extremeSearch = async (
  prompt: string,
  dataStream: DataStreamWriter,
): Promise<Research> => {
  const allSources: SearchResult[] = [];

  dataStream.writeMessageAnnotation({
    status: { title: 'Planning research' },
  });

  // plan out the research
  const { object: plan } = await generateObject({
    model: myProvider.languageModel('chat-model'),
    schema: z.object({
      plan: z
        .array(
          z.object({
            title: z
              .string()
              .min(10)
              .max(70)
              .describe('A title for the research topic'),
            todos: z
              .array(z.string())
              .min(3)
              .max(5)
              .describe('A list of what to research for the given title'),
          }),
        )
        .min(1)
        .max(3), // Reduced from 5 to 3 to avoid API rate limits
    }),
    prompt: `
Plan out the research for the following topic: ${prompt}.

Plan Guidelines:
- Break down the topic into key aspects to research
- Generate specific, diverse search queries for each aspect
- Search for relevant information using the web search tool
- Analyze the results and identify important facts and insights
- The plan is limited to 15 actions, do not exceed this limit!
- Follow up with more specific queries as you learn more
- Add todos for code execution if it is asked for by the user
- No need to synthesize your findings into a comprehensive response, just return the results
- The plan should be concise and to the point, no more than 10 items
- Keep the titles concise and to the point, no more than 70 characters
- Mention any need for visualizations in the plan
- Make the plan technical and specific to the topic`,
  });

  console.log(plan.plan);

  // calculate the total number of todos
  const totalTodos = plan.plan.reduce(
    (acc, curr) => acc + curr.todos.length,
    0,
  );
  console.log(`Total todos: ${totalTodos}`);

  dataStream.writeMessageAnnotation({
    status: { title: 'Research plan ready, starting up research agent' },
    plan: plan.plan,
  });

  const toolResults: any[] = [];

  // BOUCLE EXPLICITE SUR LE PLAN (remplace la délégation au LLM)
  for (let topicIdx = 0; topicIdx < plan.plan.length; topicIdx++) {
    const topic = plan.plan[topicIdx];
    for (let todoIdx = 0; todoIdx < topic.todos.length; todoIdx++) {
      const todo = topic.todos[todoIdx];
      // Ne traiter que les todos qui commencent par 'Search:'
      if (!todo.trim().toLowerCase().startsWith('search:')) {
        dataStream.writeMessageAnnotation({
          status: { title: `Instruction skipped (not a search query): "${todo}"` }
        });
        continue;
      }
      // Extraire la vraie requête
      const query = todo.replace(/^Search:/i, '').trim();
      if (!query) {
        dataStream.writeMessageAnnotation({
          status: { title: `Skipped empty search query in todo: "${todo}"` }
        });
        continue;
      }
      dataStream.writeMessageAnnotation({
        status: { title: `Searching the web for \"${query}\"` },
      });
      // Générer un queryId unique pour chaque recherche
      const queryId = `${topicIdx + 1}-${todoIdx + 1}-${Date.now()}-${Math.floor(Math.random()*10000)}`;
      dataStream.writeMessageAnnotation({
        type: 'search_query',
        queryId,
        query,
      });
      let results = await searchWeb(query);
      allSources.push(...results);
      results.forEach((source) => {
        dataStream.writeMessageAnnotation({
          type: 'source',
          queryId,
          source: { title: source.title, url: source.url },
        });
      });
      // Get full content for the top results
      if (results.length > 0 && serverEnv.EXA_API_KEY) {
        try {
          dataStream.writeMessageAnnotation({
            status: {
              title: `Reading content from search results for \"${query}\"`,
            },
          });
          const urls = results
            .map((r) => r.url)
            .filter(
              (url) => url && typeof url === 'string' && url.startsWith('http'),
            );
          if (urls.length > 0) {
            const contentsResults = (await Promise.race([
              getContents(urls),
              new Promise((_, reject) =>
                setTimeout(
                  () =>
                    reject(
                      new Error(
                        'Content retrieval timeout after 30 seconds',
                      ),
                    ),
                  30000,
                ),
              ),
            ])) as SearchResult[];
            if (contentsResults && contentsResults.length > 0) {
              contentsResults.forEach((content) => {
                if (content?.content) {
                  dataStream.writeMessageAnnotation({
                    type: 'content',
                    queryId,
                    content: {
                      title: content.title || 'Untitled',
                      url: content.url,
                      text: `${content.content.slice(0, 500)}...`,
                    },
                  });
                }
              });
              // Update results with full content
              results = contentsResults.map((content) => {
                const originalResult = results.find(
                  (r) => r.url === content.url,
                );
                return {
                  title: content.title || originalResult?.title || 'Untitled',
                  url: content.url,
                  content: content.content || originalResult?.content || '',
                  publishedDate:
                    content.publishedDate || originalResult?.publishedDate || '',
                  favicon: content.favicon || originalResult?.favicon || '',
                };
              }) as SearchResult[];
              dataStream.writeMessageAnnotation({
                status: {
                  title: `Content retrieved for \"${query}\" - ${contentsResults.length} sources processed`,
                },
              });
            }
          }
        } catch (error) {
          dataStream.writeMessageAnnotation({
            status: {
              title: `Content retrieval failed for \"${query}\" - using search summaries`,
            },
          });
        }
      }
      // Progress annotation
      dataStream.writeMessageAnnotation({
        status: {
          title: `Step completed (${topicIdx + 1}.${todoIdx + 1}/${totalTodos} searches done)`
        },
        stepProgress: {
          topic: topic.title,
          todo,
          topicIdx,
          todoIdx,
          completedSearches: topicIdx * topic.todos.length + todoIdx + 1,
          totalTodos
        }
      });
    }
  }

  // Pas de codeRunner ni de reasoning LLM ici, on ne fait que du search explicite
  const completedSearches = totalTodos;
  const completionRate = 100;

  dataStream.writeMessageAnnotation({
    status: {
      title: `✅ Research fully completed: ${completedSearches}/${totalTodos} searches`,
      details: `All research topics covered successfully`
    },
  });

  return {
    text: `Research completed for prompt: ${prompt}`,
    toolResults: [],
    sources: Array.from(
      new Map(
        allSources.map((s) => [
          s.url,
          { ...s, content: `${s.content.slice(0, 3000)}...` },
        ]),
      ).values(),
    ),
    charts: [],
  };
};

export const extremeSearchTool = (dataStream: DataStreamWriter) =>
  tool({
    description: 'Use this tool to conduct an extreme search on a given topic.',
    parameters: z.object({
      prompt: z
        .string()
        .describe(
          "This should take the user's exact prompt. Extract from the context but do not infer or change in any way.",
        ),
    }),
    execute: async ({ prompt }) => {
      console.log({ prompt });

      const research = await extremeSearch(prompt, dataStream);

      return {
        research: {
          text: research.text,
          toolResults: research.toolResults,
          sources: research.sources,
          charts: research.charts,
        },
      };
    },
  });
