'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle, Clock, Search, Zap } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface DebugStep {
  id: string;
  type: 'planning' | 'searching' | 'processing' | 'completed' | 'error';
  title: string;
  details?: string;
  timestamp: Date;
  duration?: number;
}

interface SearchDebugProps {
  query: string;
  onDebugStep?: (step: DebugStep) => void;
}

export function SearchDebug({ query, onDebugStep }: SearchDebugProps) {
  const [steps, setSteps] = useState<DebugStep[]>([]);
  const [isActive, setIsActive] = useState(false);
  const [currentStep, setCurrentStep] = useState<string | null>(null);

  const addStep = (step: Omit<DebugStep, 'id' | 'timestamp'>) => {
    const newStep: DebugStep = {
      ...step,
      id: `step-${Date.now()}-${Math.random()}`,
      timestamp: new Date(),
    };
    
    setSteps(prev => [...prev, newStep]);
    setCurrentStep(newStep.id);
    onDebugStep?.(newStep);
  };

  const updateStep = (stepId: string, updates: Partial<DebugStep>) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, ...updates, duration: Date.now() - step.timestamp.getTime() }
        : step
    ));
  };

  const simulateSearch = async () => {
    setIsActive(true);
    setSteps([]);

    // Étape 1: Planification
    addStep({
      type: 'planning',
      title: 'Planification de la recherche',
      details: 'Analyse de la requête et création du plan de recherche...'
    });

    await new Promise(resolve => setTimeout(resolve, 2000));

    // Étape 2: Recherches multiples
    const searchQueries = [
      'GPT-4 innovations 2024',
      'Claude AI capabilities comparison',
      'Google Gemini features 2024',
      'LLM performance benchmarks',
      'Generative AI trends 2024'
    ];

    for (let i = 0; i < searchQueries.length; i++) {
      const query = searchQueries[i];
      addStep({
        type: 'searching',
        title: `Recherche: ${query}`,
        details: `Recherche en cours pour "${query}"...`
      });

      await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 2000));

      updateStep(steps[steps.length - 1]?.id || '', {
        type: 'completed',
        details: `Trouvé ${Math.floor(Math.random() * 10) + 5} résultats pour "${query}"`
      });
    }

    // Étape 3: Traitement
    addStep({
      type: 'processing',
      title: 'Analyse et synthèse',
      details: 'Traitement des résultats et création de la synthèse...'
    });

    await new Promise(resolve => setTimeout(resolve, 4000));

    // Étape finale
    addStep({
      type: 'completed',
      title: 'Recherche terminée',
      details: 'Analyse complète des innovations en IA générative 2024'
    });

    setIsActive(false);
    setCurrentStep(null);
  };

  const getStepIcon = (type: DebugStep['type']) => {
    switch (type) {
      case 'planning':
        return <Zap className="h-4 w-4 text-blue-500" />;
      case 'searching':
        return <Search className="h-4 w-4 text-orange-500" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-purple-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStepColor = (type: DebugStep['type']) => {
    switch (type) {
      case 'planning':
        return 'bg-blue-50 border-blue-200';
      case 'searching':
        return 'bg-orange-50 border-orange-200';
      case 'processing':
        return 'bg-purple-50 border-purple-200';
      case 'completed':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Debug de Recherche Extrême</span>
          <div className="flex items-center space-x-2">
            {isActive && (
              <Badge variant="secondary" className="animate-pulse">
                En cours...
              </Badge>
            )}
            <Button 
              onClick={simulateSearch} 
              disabled={isActive}
              size="sm"
            >
              {isActive ? 'Recherche en cours...' : 'Démarrer la recherche'}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="text-sm text-gray-600 mb-4">
            <strong>Requête:</strong> {query || 'Aucune requête spécifiée'}
          </div>

          <AnimatePresence>
            {steps.map((step, index) => (
              <motion.div
                key={step.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
                className={`p-3 rounded-lg border ${getStepColor(step.type)} ${
                  currentStep === step.id ? 'ring-2 ring-blue-300' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {getStepIcon(step.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900">
                        {step.title}
                      </h4>
                      <div className="flex items-center space-x-2">
                        {step.duration && (
                          <Badge variant="outline" className="text-xs">
                            {(step.duration / 1000).toFixed(1)}s
                          </Badge>
                        )}
                        <span className="text-xs text-gray-500">
                          {step.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                    {step.details && (
                      <p className="text-xs text-gray-600 mt-1">
                        {step.details}
                      </p>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>

          {steps.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Aucune étape de recherche en cours</p>
              <p className="text-xs">Cliquez sur "Démarrer la recherche" pour commencer</p>
            </div>
          )}
        </div>

        {/* Statistiques */}
        {steps.length > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-blue-600">
                  {steps.filter(s => s.type === 'planning').length}
                </div>
                <div className="text-xs text-gray-500">Planification</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-orange-600">
                  {steps.filter(s => s.type === 'searching').length}
                </div>
                <div className="text-xs text-gray-500">Recherches</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-green-600">
                  {steps.filter(s => s.type === 'completed').length}
                </div>
                <div className="text-xs text-gray-500">Terminées</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-red-600">
                  {steps.filter(s => s.type === 'error').length}
                </div>
                <div className="text-xs text-gray-500">Erreurs</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
