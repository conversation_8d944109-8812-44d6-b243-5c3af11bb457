'use client';

import React, { useState } from 'react';
import { ReasoningPartView, type ReasoningPart } from './reasoning-part';

/**
 * Exemple d'utilisation du composant ReasoningPartView
 * Ce composant montre comment intégrer et utiliser ReasoningPartView
 */
export function ReasoningExample() {
  // État pour gérer l'expansion et le plein écran
  const [isExpanded, setIsExpanded] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Exemple de données de raisonnement
  const reasoningPart: ReasoningPart = {
    type: 'reasoning',
    reasoning: 'Analyse de la demande utilisateur...',
    details: [
      {
        type: 'text',
        text: '## Étape 1: Analyse du contexte\n\nJe commence par analyser le contexte de la demande pour comprendre les besoins spécifiques de l\'utilisateur.'
      },
      {
        type: 'text',
        text: '## Étape 2: Recherche d\'informations\n\nJe recherche les informations pertinentes dans la base de connaissances et les sources disponibles.'
      },
      {
        type: 'text',
        text: '## Étape 3: Synthèse\n\nJe synthétise les informations trouvées pour formuler une réponse complète et précise.'
      }
    ]
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold mb-4">Exemple d'utilisation de ReasoningPartView</h1>
      
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Raisonnement en cours (isComplete: false)</h2>
        <ReasoningPartView
          part={reasoningPart}
          sectionKey="reasoning-in-progress"
          isComplete={false}
          duration={null}
          parallelTool="web-search"
          isExpanded={isExpanded}
          isFullscreen={isFullscreen}
          setIsFullscreen={setIsFullscreen}
          setIsExpanded={setIsExpanded}
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Raisonnement terminé (isComplete: true)</h2>
        <ReasoningPartView
          part={reasoningPart}
          sectionKey="reasoning-complete"
          isComplete={true}
          duration="2.3s"
          parallelTool={null}
          isExpanded={true}
          isFullscreen={false}
          setIsFullscreen={() => {}}
          setIsExpanded={() => {}}
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Contrôles</h2>
        <div className="flex gap-4">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            {isExpanded ? 'Réduire' : 'Étendre'}
          </button>
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            {isFullscreen ? 'Mode normal' : 'Plein écran'}
          </button>
        </div>
      </div>
    </div>
  );
}

/**
 * Exemple d'intégration dans un système de chat
 */
export function ChatWithReasoning() {
  const [reasoningStates, setReasoningStates] = useState<{
    [key: string]: { isExpanded: boolean; isFullscreen: boolean }
  }>({});

  const updateReasoningState = (key: string, updates: Partial<{ isExpanded: boolean; isFullscreen: boolean }>) => {
    setReasoningStates(prev => ({
      ...prev,
      [key]: { ...prev[key], ...updates }
    }));
  };

  // Exemple de message avec raisonnement
  const messageWithReasoning = {
    id: 'msg-1',
    role: 'assistant' as const,
    content: 'Voici ma réponse après analyse...',
    parts: [
      {
        type: 'reasoning' as const,
        reasoning: 'Processus de réflexion...',
        details: [
          {
            type: 'text' as const,
            text: 'Analyse des données disponibles...'
          }
        ]
      },
      {
        type: 'text' as const,
        text: 'Voici ma réponse finale basée sur l\'analyse.'
      }
    ]
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Chat avec Raisonnement</h1>
      
      <div className="space-y-4">
        {messageWithReasoning.parts?.map((part, index) => {
          const key = `${messageWithReasoning.id}-part-${index}`;
          
          if (part.type === 'reasoning') {
            const state = reasoningStates[key] || { isExpanded: true, isFullscreen: false };
            
            return (
              <ReasoningPartView
                key={key}
                part={part as ReasoningPart}
                sectionKey={key}
                isComplete={true}
                duration="1.8s"
                parallelTool={null}
                isExpanded={state.isExpanded}
                isFullscreen={state.isFullscreen}
                setIsFullscreen={(value) => updateReasoningState(key, { isFullscreen: value })}
                setIsExpanded={(value) => updateReasoningState(key, { isExpanded: value })}
              />
            );
          }
          
          if (part.type === 'text') {
            return (
              <div key={key} className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
                <p>{part.text}</p>
              </div>
            );
          }
          
          return null;
        })}
      </div>
    </div>
  );
}
