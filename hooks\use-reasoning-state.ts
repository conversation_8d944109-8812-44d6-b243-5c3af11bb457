import { useState, useCallback } from 'react';

interface ReasoningState {
  isExpanded: boolean;
  isFullscreen: boolean;
}

interface ReasoningStates {
  [key: string]: ReasoningState;
}

/**
 * Hook pour gérer l'état des composants de raisonnement
 * Permet de maintenir l'état d'expansion et de plein écran pour chaque section de raisonnement
 */
export function useReasoningState() {
  const [reasoningStates, setReasoningStates] = useState<ReasoningStates>({});

  const getReasoningState = useCallback((key: string): ReasoningState => {
    return reasoningStates[key] || { isExpanded: true, isFullscreen: false };
  }, [reasoningStates]);

  const updateReasoningState = useCallback((
    key: string, 
    updates: Partial<ReasoningState>
  ) => {
    setReasoningStates(prev => ({
      ...prev,
      [key]: { ...prev[key], ...updates }
    }));
  }, []);

  const setIsExpanded = useCallback((key: string, isExpanded: boolean) => {
    updateReasoningState(key, { isExpanded });
  }, [updateReasoningState]);

  const setIsFullscreen = useCallback((key: string, isFullscreen: boolean) => {
    updateReasoningState(key, { isFullscreen });
  }, [updateReasoningState]);

  const resetReasoningState = useCallback((key: string) => {
    setReasoningStates(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  }, []);

  const resetAllReasoningStates = useCallback(() => {
    setReasoningStates({});
  }, []);

  return {
    getReasoningState,
    updateReasoningState,
    setIsExpanded,
    setIsFullscreen,
    resetReasoningState,
    resetAllReasoningStates,
  };
}
