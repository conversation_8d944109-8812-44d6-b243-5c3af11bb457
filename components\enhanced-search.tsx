'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, AlertCircle, RefreshCw, ExternalLink, Clock, Globe } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from './toast';

interface SearchResult {
  title: string;
  url: string;
  content: string;
  publishedDate?: string;
  favicon?: string;
}

interface SearchError {
  message: string;
  code?: string;
  retryable: boolean;
}

interface EnhancedSearchProps {
  query: string;
  onResults?: (results: SearchResult[]) => void;
  onError?: (error: SearchError) => void;
  maxResults?: number;
  autoSearch?: boolean;
}

export function EnhancedSearch({
  query,
  onResults,
  onError,
  maxResults = 10,
  autoSearch = true,
}: EnhancedSearchProps) {
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<SearchError | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [searchStartTime, setSearchStartTime] = useState<number | null>(null);

  const MAX_RETRIES = 3;
  const RETRY_DELAY = 1000; // 1 second

  const performSearch = useCallback(async (searchQuery: string, attempt = 0) => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    setError(null);
    setSearchStartTime(Date.now());

    try {
      console.log(`Enhanced Search: Performing search for "${searchQuery}" (attempt ${attempt + 1})`);

      // Try multiple search endpoints with fallback
      const searchEndpoints = [
        '/api/web-search',
        '/api/extreme-search',
        '/api/tavily-search'
      ];

      let lastError: Error | null = null;

      for (const endpoint of searchEndpoints) {
        try {
          const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: searchQuery,
              maxResults,
              language: 'fr',
            }),
            signal: AbortSignal.timeout(30000), // 30 second timeout
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          
          if (data.error) {
            throw new Error(data.error);
          }

          const searchResults = data.results || data.data || [];
          
          if (searchResults.length > 0) {
            console.log(`Enhanced Search: Found ${searchResults.length} results from ${endpoint}`);
            setResults(searchResults);
            setRetryCount(0);
            onResults?.(searchResults);
            return;
          }
        } catch (err) {
          console.warn(`Enhanced Search: ${endpoint} failed:`, err);
          lastError = err as Error;
          continue;
        }
      }

      // If all endpoints failed, throw the last error
      throw lastError || new Error('All search endpoints failed');

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('Enhanced Search Error:', err);

      const searchError: SearchError = {
        message: errorMessage,
        code: err instanceof Error && 'code' in err ? (err as any).code : undefined,
        retryable: attempt < MAX_RETRIES && !errorMessage.includes('timeout'),
      };

      setError(searchError);
      onError?.(searchError);

      // Auto-retry for retryable errors
      if (searchError.retryable && attempt < MAX_RETRIES) {
        const delay = RETRY_DELAY * Math.pow(2, attempt); // Exponential backoff
        console.log(`Enhanced Search: Retrying in ${delay}ms...`);
        
        setTimeout(() => {
          setRetryCount(attempt + 1);
          performSearch(searchQuery, attempt + 1);
        }, delay);
      } else {
        toast({
          type: 'error',
          description: `Recherche échouée: ${errorMessage}`,
        });
      }
    } finally {
      setLoading(false);
    }
  }, [maxResults, onResults, onError]);

  useEffect(() => {
    if (autoSearch && query) {
      performSearch(query);
    }
  }, [query, autoSearch, performSearch]);

  const handleRetry = () => {
    setRetryCount(0);
    performSearch(query);
  };

  const formatDuration = () => {
    if (!searchStartTime) return '';
    const duration = Date.now() - searchStartTime;
    return `${(duration / 1000).toFixed(1)}s`;
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-3">
            <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />
            <div className="text-sm text-gray-600">
              Recherche en cours{retryCount > 0 && ` (tentative ${retryCount + 1}/${MAX_RETRIES + 1})`}...
            </div>
          </div>
          {searchStartTime && (
            <div className="text-xs text-gray-400 text-center mt-2">
              <Clock className="h-3 w-3 inline mr-1" />
              {formatDuration()}
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-red-200 bg-red-50">
        <CardContent className="p-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-800">
                Erreur de recherche
              </h3>
              <p className="text-sm text-red-600 mt-1">{error.message}</p>
              {error.retryable && (
                <Button
                  onClick={handleRetry}
                  variant="outline"
                  size="sm"
                  className="mt-3"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Réessayer
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (results.length === 0) {
    return (
      <Card className="w-full">
        <CardContent className="p-6 text-center">
          <Search className="h-8 w-8 text-gray-400 mx-auto mb-3" />
          <p className="text-sm text-gray-600">
            Aucun résultat trouvé pour "{query}"
          </p>
          <Button
            onClick={handleRetry}
            variant="outline"
            size="sm"
            className="mt-3"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Rechercher à nouveau
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-lg">
          <span>Résultats de recherche</span>
          <Badge variant="secondary">{results.length} résultats</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <AnimatePresence>
          {results.map((result, index) => (
            <motion.div
              key={result.url}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="border-b border-gray-100 last:border-b-0 p-4 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  {result.favicon ? (
                    <img
                      src={result.favicon}
                      alt=""
                      className="w-4 h-4"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  ) : (
                    <Globe className="h-4 w-4 text-gray-400" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900 mb-1">
                    <a
                      href={result.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-blue-600 transition-colors"
                    >
                      {result.title}
                      <ExternalLink className="h-3 w-3 inline ml-1" />
                    </a>
                  </h3>
                  <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                    {result.content}
                  </p>
                  <div className="flex items-center space-x-2 text-xs text-gray-400">
                    <span>{new URL(result.url).hostname}</span>
                    {result.publishedDate && (
                      <>
                        <span>•</span>
                        <span>{result.publishedDate}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
}
