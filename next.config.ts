import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  experimental: {
    ppr: true,
    // Optimiser les imports de packages pour améliorer les performances
    optimizePackageImports: [
      'react-markdown',
      'react-syntax-highlighter',
      'remark-gfm',
    ],
  },
  // Activer la détection automatique des liens
  reactStrictMode: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  images: {
    remotePatterns: [
      {
        hostname: 'avatar.vercel.sh',
      },
      {
        hostname: 'mjyzhrfmepx1uly6.public.blob.vercel-storage.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'images.unsplash.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: '**.unsplash.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 't2.gstatic.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'images.pexels.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: '**.pexels.com',
        protocol: 'https',
        pathname: '/**',
      },
      // Google Favicons API
      {
        hostname: 'www.google.com',
        protocol: 'https',
        pathname: '/s2/favicons/**',
      },
      {
        hostname: 'google.com',
        protocol: 'https',
        pathname: '/s2/favicons/**',
      },
      // Autoriser toutes les images via le proxy
      {
        hostname: 'localhost',
        protocol: 'http',
        pathname: '/api/proxy-image/**',
      },
      {
        // Pour l'environnement de production
        hostname: process.env.VERCEL_URL || '',
        protocol: 'https',
        pathname: '/api/proxy-image/**',
      },
    ],
  },
};

export default nextConfig;
