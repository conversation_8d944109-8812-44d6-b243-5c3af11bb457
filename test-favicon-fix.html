<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Correction des Favicons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-case {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        img {
            width: 24px;
            height: 24px;
            margin: 5px;
            border: 1px solid #ddd;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Test de Correction des Favicons</h1>
    <p>Ce test vérifie que les corrections apportées au composant ExtremeSearch résolvent l'erreur "Expected a non-empty string".</p>

    <div class="test-case">
        <div class="test-title">Test 1: Fonction getSafeHostname</div>
        <div class="code" id="test1-code"></div>
        <div id="test1-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">Test 2: Fonction getSafeFaviconUrl</div>
        <div class="code" id="test2-code"></div>
        <div id="test2-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">Test 3: URLs de favicon valides</div>
        <div class="code" id="test3-code"></div>
        <div id="test3-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">Test 4: Rendu d'images avec URLs corrigées</div>
        <div id="test4-result"></div>
    </div>

    <script>
        // Reproduction des fonctions corrigées
        const getSafeHostname = (url) => {
            try {
                if (!url || typeof url !== 'string' || url.trim() === '') {
                    return 'example.com';
                }
                const hostname = new URL(url).hostname;
                return hostname || 'example.com';
            } catch {
                return 'example.com';
            }
        };

        const getSafeFaviconUrl = (favicon, fallbackUrl) => {
            if (!favicon || typeof favicon !== 'string' || favicon.trim() === '') {
                return `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(getSafeHostname(fallbackUrl))}`;
            }
            
            if (!favicon.startsWith('http')) {
                return `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(getSafeHostname(fallbackUrl))}`;
            }
            
            return favicon;
        };

        // Test 1: getSafeHostname
        function test1() {
            const testCases = [
                { input: '', expected: 'example.com' },
                { input: null, expected: 'example.com' },
                { input: undefined, expected: 'example.com' },
                { input: 'invalid-url', expected: 'example.com' },
                { input: 'https://google.com', expected: 'google.com' },
                { input: 'https://www.github.com/user/repo', expected: 'www.github.com' }
            ];

            let results = [];
            let allPassed = true;

            testCases.forEach((testCase, index) => {
                try {
                    const result = getSafeHostname(testCase.input);
                    const passed = result === testCase.expected;
                    if (!passed) allPassed = false;
                    
                    results.push(`Test 1.${index + 1}: ${passed ? '✅' : '❌'} Input: ${JSON.stringify(testCase.input)} → Output: "${result}" (Expected: "${testCase.expected}")`);
                } catch (error) {
                    allPassed = false;
                    results.push(`Test 1.${index + 1}: ❌ Error: ${error.message}`);
                }
            });

            document.getElementById('test1-code').textContent = 'getSafeHostname(url) - Tests avec URLs invalides et valides';
            document.getElementById('test1-result').innerHTML = `
                <div class="${allPassed ? 'success' : 'error'}">
                    <strong>${allPassed ? 'SUCCÈS' : 'ÉCHEC'}</strong>: ${results.length} tests
                    <ul><li>${results.join('</li><li>')}</li></ul>
                </div>
            `;
        }

        // Test 2: getSafeFaviconUrl
        function test2() {
            const testCases = [
                { favicon: '', url: 'https://google.com', shouldContain: 'google.com' },
                { favicon: null, url: 'https://github.com', shouldContain: 'github.com' },
                { favicon: undefined, url: 'https://stackoverflow.com', shouldContain: 'stackoverflow.com' },
                { favicon: 'relative-path.ico', url: 'https://example.com', shouldContain: 'example.com' },
                { favicon: 'https://valid-favicon.com/icon.ico', url: 'https://example.com', expected: 'https://valid-favicon.com/icon.ico' }
            ];

            let results = [];
            let allPassed = true;

            testCases.forEach((testCase, index) => {
                try {
                    const result = getSafeFaviconUrl(testCase.favicon, testCase.url);
                    let passed;
                    
                    if (testCase.expected) {
                        passed = result === testCase.expected;
                    } else {
                        passed = result.includes(testCase.shouldContain) && result.startsWith('https://');
                    }
                    
                    if (!passed) allPassed = false;
                    
                    results.push(`Test 2.${index + 1}: ${passed ? '✅' : '❌'} Favicon: ${JSON.stringify(testCase.favicon)} → "${result}"`);
                } catch (error) {
                    allPassed = false;
                    results.push(`Test 2.${index + 1}: ❌ Error: ${error.message}`);
                }
            });

            document.getElementById('test2-code').textContent = 'getSafeFaviconUrl(favicon, fallbackUrl) - Tests avec favicons invalides et valides';
            document.getElementById('test2-result').innerHTML = `
                <div class="${allPassed ? 'success' : 'error'}">
                    <strong>${allPassed ? 'SUCCÈS' : 'ÉCHEC'}</strong>: ${results.length} tests
                    <ul><li>${results.join('</li><li>')}</li></ul>
                </div>
            `;
        }

        // Test 3: URLs de favicon valides
        function test3() {
            const testUrls = [
                getSafeFaviconUrl('', 'https://google.com'),
                getSafeFaviconUrl(null, 'https://github.com'),
                getSafeFaviconUrl(undefined, 'https://stackoverflow.com'),
                getSafeFaviconUrl('invalid', 'https://example.com'),
                getSafeFaviconUrl('https://valid.com/icon.ico', 'https://fallback.com')
            ];

            let results = [];
            let allPassed = true;

            testUrls.forEach((url, index) => {
                try {
                    // Vérifier que l'URL n'est pas vide et commence par https://
                    const isValid = url && typeof url === 'string' && url.trim() !== '' && url.startsWith('https://');
                    if (!isValid) allPassed = false;
                    
                    results.push(`URL ${index + 1}: ${isValid ? '✅' : '❌'} "${url}"`);
                } catch (error) {
                    allPassed = false;
                    results.push(`URL ${index + 1}: ❌ Error: ${error.message}`);
                }
            });

            document.getElementById('test3-code').textContent = 'Validation des URLs générées - Toutes doivent être des URLs HTTPS valides';
            document.getElementById('test3-result').innerHTML = `
                <div class="${allPassed ? 'success' : 'error'}">
                    <strong>${allPassed ? 'SUCCÈS' : 'ÉCHEC'}</strong>: Toutes les URLs sont valides
                    <ul><li>${results.join('</li><li>')}</li></ul>
                </div>
            `;
        }

        // Test 4: Rendu d'images
        function test4() {
            const testSources = [
                { url: 'https://google.com', favicon: '' },
                { url: 'https://github.com', favicon: null },
                { url: 'https://stackoverflow.com', favicon: undefined },
                { url: 'https://example.com', favicon: 'invalid-path' },
                { url: 'https://openai.com', favicon: 'https://openai.com/favicon.ico' }
            ];

            let imagesHtml = '';
            let results = [];

            testSources.forEach((source, index) => {
                try {
                    const faviconUrl = getSafeFaviconUrl(source.favicon, source.url);
                    imagesHtml += `<img src="${faviconUrl}" alt="Favicon ${index + 1}" title="${faviconUrl}" onerror="this.style.border='2px solid red';" onload="this.style.border='2px solid green';">`;
                    results.push(`Image ${index + 1}: URL générée avec succès`);
                } catch (error) {
                    results.push(`Image ${index + 1}: ❌ Error: ${error.message}`);
                }
            });

            document.getElementById('test4-result').innerHTML = `
                <div class="info">
                    <strong>Test de rendu d'images:</strong>
                    <div style="margin: 10px 0;">${imagesHtml}</div>
                    <small>Bordure verte = image chargée avec succès, bordure rouge = erreur de chargement</small>
                    <ul><li>${results.join('</li><li>')}</li></ul>
                </div>
            `;
        }

        // Exécuter tous les tests
        function runAllTests() {
            test1();
            test2();
            test3();
            test4();
        }

        // Exécuter les tests au chargement de la page
        window.onload = runAllTests;
    </script>

    <div class="test-case">
        <div class="test-title">📋 Résumé de la Correction</div>
        <div class="info">
            <strong>Problème résolu:</strong> TypeError: Expected a non-empty string
            <br><br>
            <strong>Cause:</strong> Le composant ExtremeSourceCard recevait des URLs de favicon vides ou invalides
            <br><br>
            <strong>Solution:</strong>
            <ul>
                <li>✅ Fonction <code>getSafeHostname()</code> qui ne retourne jamais de chaîne vide</li>
                <li>✅ Fonction <code>getSafeFaviconUrl()</code> qui valide et corrige les URLs de favicon</li>
                <li>✅ Validation des URLs avant utilisation dans les composants Image</li>
                <li>✅ Fallback vers Google Favicons API pour les URLs invalides</li>
            </ul>
        </div>
    </div>
</body>
</html>
