// Test final pour vérifier que l'erreur TypeError est résolue
// Utilise fetch natif de Node.js 18+

async function testFaviconError() {
  console.log('🔧 Test final de correction de l\'erreur favicon...');
  
  const testQuery = 'Fais une recherche extrême sur les dernières innovations en intelligence artificielle générative en 2024, en comparant GPT-4, <PERSON> et <PERSON>';
  
  try {
    console.log('📡 Envoi de la requête...');
    
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: testQuery,
          }
        ],
        id: 'test-favicon-' + Date.now(),
        selectedChatModel: 'chat-model',
      }),
    });

    console.log(`📊 Statut de la réponse: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log('📖 Surveillance des erreurs dans le stream...');
    
    let chunkCount = 0;
    let hasTypeError = false;
    let hasExtremeSearch = false;
    let hasSourceCards = false;
    let errorMessages = [];
    
    const reader = response.body.getReader();
    
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log(`✅ Stream terminé après ${chunkCount} chunks`);
        break;
      }

      chunkCount++;
      const chunk = new TextDecoder().decode(value);
      
      // Détecter les erreurs TypeError
      if (chunk.includes('TypeError') || chunk.includes('Expected a non-empty string')) {
        hasTypeError = true;
        errorMessages.push(`Chunk ${chunkCount}: ${chunk.substring(0, 200)}...`);
        console.log(`❌ TypeError détecté dans le chunk ${chunkCount}`);
      }
      
      // Détecter l'utilisation d'ExtremeSearch
      if (chunk.includes('extremeSearch') || chunk.includes('ExtremeSearch')) {
        hasExtremeSearch = true;
        console.log(`🔍 ExtremeSearch détecté dans le chunk ${chunkCount}`);
      }
      
      // Détecter les cartes de sources
      if (chunk.includes('ExtremeSourceCard') || chunk.includes('source') && chunk.includes('favicon')) {
        hasSourceCards = true;
        console.log(`🃏 Cartes de sources détectées dans le chunk ${chunkCount}`);
      }
      
      // Arrêter après un nombre raisonnable de chunks
      if (chunkCount > 100) {
        console.log('⏹️ Arrêt du test après 100 chunks');
        break;
      }
    }
    
    // Analyse des résultats
    console.log('\n📋 Résultats du test:');
    console.log(`- Chunks traités: ${chunkCount}`);
    console.log(`- TypeError détecté: ${hasTypeError ? '❌' : '✅'}`);
    console.log(`- ExtremeSearch utilisé: ${hasExtremeSearch ? '✅' : '❌'}`);
    console.log(`- Cartes de sources: ${hasSourceCards ? '✅' : '❌'}`);
    
    if (errorMessages.length > 0) {
      console.log('\n🚨 Messages d\'erreur détectés:');
      errorMessages.forEach((msg, index) => {
        console.log(`${index + 1}. ${msg}`);
      });
    }
    
    // Résultat final
    if (!hasTypeError && hasExtremeSearch) {
      console.log('\n🎉 Test RÉUSSI: Aucune erreur TypeError détectée!');
      console.log('✅ La correction des favicons fonctionne correctement');
      return true;
    } else if (hasTypeError) {
      console.log('\n❌ Test ÉCHOUÉ: TypeError encore présent');
      console.log('❌ La correction des favicons n\'est pas complète');
      return false;
    } else {
      console.log('\n⚠️ Test PARTIEL: Pas d\'erreur mais ExtremeSearch non utilisé');
      console.log('ℹ️ Peut-être que la recherche n\'a pas été déclenchée');
      return true; // Pas d'erreur = succès partiel
    }
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    return false;
  }
}

// Test des fonctions utilitaires
function testUtilityFunctions() {
  console.log('\n🧪 Test des fonctions utilitaires...');
  
  // Simulation des fonctions (copie du code corrigé)
  const getSafeHostname = (url) => {
    try {
      if (!url || typeof url !== 'string' || url.trim() === '') {
        return 'example.com';
      }
      const hostname = new URL(url).hostname;
      return hostname || 'example.com';
    } catch {
      return 'example.com';
    }
  };

  const getSafeFaviconUrl = (favicon, fallbackUrl) => {
    try {
      const safeHostname = getSafeHostname(fallbackUrl);
      const defaultFavicon = `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(safeHostname)}`;
      
      if (!favicon || typeof favicon !== 'string' || favicon.trim() === '') {
        return defaultFavicon;
      }
      
      if (!favicon.startsWith('http')) {
        return defaultFavicon;
      }
      
      try {
        new URL(favicon);
        return favicon;
      } catch {
        return defaultFavicon;
      }
    } catch {
      return 'https://www.google.com/s2/favicons?sz=128&domain=example.com';
    }
  };

  // Tests
  const testCases = [
    { favicon: '', url: 'https://google.com' },
    { favicon: null, url: 'https://github.com' },
    { favicon: undefined, url: 'https://stackoverflow.com' },
    { favicon: 'invalid', url: 'https://example.com' },
    { favicon: 'https://valid.com/icon.ico', url: 'https://fallback.com' },
    { favicon: '', url: '' }, // Cas extrême
    { favicon: null, url: null }, // Cas extrême
  ];

  let allPassed = true;
  
  testCases.forEach((testCase, index) => {
    try {
      const result = getSafeFaviconUrl(testCase.favicon, testCase.url);
      const isValid = result && 
                     typeof result === 'string' && 
                     result.trim() !== '' && 
                     result.startsWith('https://');
      
      if (!isValid) {
        allPassed = false;
        console.log(`❌ Test ${index + 1}: URL invalide générée: "${result}"`);
      } else {
        console.log(`✅ Test ${index + 1}: URL valide générée`);
      }
    } catch (error) {
      allPassed = false;
      console.log(`❌ Test ${index + 1}: Erreur: ${error.message}`);
    }
  });

  console.log(`\n📊 Tests utilitaires: ${allPassed ? '✅ TOUS RÉUSSIS' : '❌ CERTAINS ÉCHOUÉS'}`);
  return allPassed;
}

// Fonction pour vérifier que le serveur est démarré
async function waitForServer(maxAttempts = 5) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch('http://localhost:3000/api/health', {
        method: 'GET',
      });
      if (response.ok) {
        console.log('✅ Serveur prêt');
        return true;
      }
    } catch (error) {
      console.log(`⏳ Tentative ${i + 1}/${maxAttempts}: Serveur non prêt, attente...`);
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  console.log('❌ Serveur non accessible après plusieurs tentatives');
  return false;
}

// Exécuter le test
async function main() {
  console.log('🔧 Test final de correction de l\'erreur favicon TypeError');
  console.log('='.repeat(60));
  
  // Test des fonctions utilitaires d'abord
  const utilityTestsPassed = testUtilityFunctions();
  
  // Test du serveur
  console.log('\n🔧 Vérification du serveur...');
  const serverReady = await waitForServer();
  
  if (!serverReady) {
    console.log('❌ Impossible de se connecter au serveur. Assurez-vous que npm run dev est lancé.');
    process.exit(1);
  }
  
  // Test principal
  const mainTestPassed = await testFaviconError();
  
  // Résultat final
  console.log('\n' + '='.repeat(60));
  const overallSuccess = utilityTestsPassed && mainTestPassed;
  console.log(overallSuccess ? 
    '🎉 SUCCÈS COMPLET: Erreur favicon corrigée!' : 
    '❌ ÉCHEC: Des problèmes persistent'
  );
  console.log('='.repeat(60));
  
  process.exit(overallSuccess ? 0 : 1);
}

main().catch(error => {
  console.error('💥 Erreur fatale:', error);
  process.exit(1);
});
