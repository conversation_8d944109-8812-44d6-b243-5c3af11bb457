'use client';

import React, { useState } from 'react';
import { SearchDebug } from '@/components/search-debug';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Bug, Zap, RefreshCw } from 'lucide-react';

export default function DebugSearchPage() {
  const [query, setQuery] = useState('Fais une recherche extrême sur les dernières innovations en intelligence artificielle générative en 2024, en comparant GPT-4, <PERSON> et Gemini');
  const [debugLogs, setDebugLogs] = useState<string[]>([]);
  const [isDebugging, setIsDebugging] = useState(false);

  const addDebugLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]);
  };

  const testExtremeSearch = async () => {
    setIsDebugging(true);
    addDebugLog('🚀 Démarrage du test de recherche extrême');

    try {
      // Simuler l'appel à l'API de chat avec la recherche extrême
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [
            {
              role: 'user',
              content: query,
            }
          ],
          id: 'debug-chat-' + Date.now(),
          selectedChatModel: 'chat-model',
        }),
      });

      addDebugLog(`📡 Réponse API: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Lire le stream de réponse
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Pas de reader disponible');
      }

      addDebugLog('📖 Lecture du stream de réponse...');
      let chunkCount = 0;

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          addDebugLog(`✅ Stream terminé après ${chunkCount} chunks`);
          break;
        }

        chunkCount++;
        const chunk = new TextDecoder().decode(value);
        
        // Log des chunks importants
        if (chunk.includes('extremeSearch') || chunk.includes('status') || chunk.includes('error')) {
          addDebugLog(`📦 Chunk ${chunkCount}: ${chunk.substring(0, 100)}...`);
        }

        // Vérifier si le stream s'arrête prématurément
        if (chunkCount > 100) {
          addDebugLog('⚠️ Stream très long, possible problème de performance');
        }
      }

    } catch (error) {
      addDebugLog(`❌ Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      setIsDebugging(false);
      addDebugLog('🏁 Test terminé');
    }
  };

  const clearLogs = () => {
    setDebugLogs([]);
    addDebugLog('🧹 Logs effacés');
  };

  const testQueries = [
    'Fais une recherche extrême sur les dernières innovations en intelligence artificielle générative en 2024, en comparant GPT-4, Claude et Gemini',
    'Recherche approfondie sur les nouvelles architectures de transformers en 2024',
    'Analyse comparative des modèles multimodaux GPT-4V vs Claude 3 vs Gemini Pro Vision',
    'Innovations en IA générative pour le code et la programmation en 2024',
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bug className="h-6 w-6 text-red-500" />
              <span>Debug de Recherche Extrême</span>
              <Badge variant="destructive">Diagnostic</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Requête de test
                </label>
                <Textarea
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="Entrez votre requête de recherche..."
                  rows={3}
                />
              </div>
              <div className="flex space-x-2">
                <Button 
                  onClick={testExtremeSearch} 
                  disabled={isDebugging}
                  className="flex items-center space-x-2"
                >
                  {isDebugging ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Zap className="h-4 w-4" />
                  )}
                  <span>
                    {isDebugging ? 'Test en cours...' : 'Tester la recherche extrême'}
                  </span>
                </Button>
                <Button variant="outline" onClick={clearLogs}>
                  Effacer les logs
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Requêtes prédéfinies */}
        <Card>
          <CardHeader>
            <CardTitle>Requêtes de test prédéfinies</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {testQueries.map((testQuery, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="text-left h-auto p-3 justify-start"
                  onClick={() => setQuery(testQuery)}
                >
                  <div className="text-sm">{testQuery}</div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Simulation de recherche */}
          <SearchDebug 
            query={query}
            onDebugStep={(step) => {
              addDebugLog(`🔍 ${step.title}: ${step.details || 'Aucun détail'}`);
            }}
          />

          {/* Logs de debug */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Logs de Debug</span>
                <Badge variant="secondary">{debugLogs.length} entrées</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1 max-h-96 overflow-y-auto">
                {debugLogs.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <AlertTriangle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Aucun log disponible</p>
                    <p className="text-xs">Lancez un test pour voir les logs</p>
                  </div>
                ) : (
                  debugLogs.map((log, index) => (
                    <div
                      key={index}
                      className="text-xs font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded border-l-2 border-blue-300"
                    >
                      {log}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Informations de diagnostic */}
        <Card>
          <CardHeader>
            <CardTitle>Informations de Diagnostic</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <strong>Problème identifié:</strong>
                <p className="text-gray-600">
                  Le processus de recherche extrême s'arrête prématurément et n'affiche pas 
                  le raisonnement étape par étape.
                </p>
              </div>
              <div>
                <strong>Causes possibles:</strong>
                <ul className="text-gray-600 list-disc list-inside">
                  <li>Timeout trop court</li>
                  <li>Limite de steps insuffisante</li>
                  <li>Problème de streaming</li>
                  <li>Erreur dans le tool</li>
                </ul>
              </div>
              <div>
                <strong>Solutions testées:</strong>
                <ul className="text-gray-600 list-disc list-inside">
                  <li>Augmentation du timeout</li>
                  <li>Amélioration du streaming</li>
                  <li>Debug des étapes</li>
                  <li>Gestion d'erreurs renforcée</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
