'use client';

import React, { useState } from 'react';
import { EnhancedSearch } from '@/components/enhanced-search';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Zap, AlertTriangle, CheckCircle } from 'lucide-react';

interface SearchResult {
  title: string;
  url: string;
  content: string;
  publishedDate?: string;
  favicon?: string;
}

interface SearchError {
  message: string;
  code?: string;
  retryable: boolean;
}

export default function TestSearchPage() {
  const [query, setQuery] = useState('');
  const [currentQuery, setCurrentQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [error, setError] = useState<SearchError | null>(null);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);

  const handleSearch = () => {
    if (query.trim()) {
      setCurrentQuery(query.trim());
      setSearchHistory(prev => [query.trim(), ...prev.slice(0, 4)]);
    }
  };

  const handleResults = (searchResults: SearchResult[]) => {
    setResults(searchResults);
    setError(null);
  };

  const handleError = (searchError: SearchError) => {
    setError(searchError);
    setResults([]);
  };

  const predefinedQueries = [
    'Fais une recherche extrême sur les dernières innovations en intelligence artificielle générative en 2024, en comparant GPT-4, Claude et Gemini',
    'Innovations en IA générative 2024',
    'Comparaison GPT-4 vs Claude vs Gemini',
    'Nouvelles architectures de transformers 2024',
    'Multimodal AI developments 2024',
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-6 w-6 text-blue-500" />
              <span>Test de Recherche Avancée</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-2">
              <Input
                placeholder="Entrez votre requête de recherche..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1"
              />
              <Button onClick={handleSearch} disabled={!query.trim()}>
                <Search className="h-4 w-4 mr-2" />
                Rechercher
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Requêtes prédéfinies */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Requêtes de test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {predefinedQueries.map((predefinedQuery, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="text-left h-auto p-3 justify-start"
                  onClick={() => {
                    setQuery(predefinedQuery);
                    setCurrentQuery(predefinedQuery);
                    setSearchHistory(prev => [predefinedQuery, ...prev.slice(0, 4)]);
                  }}
                >
                  <div className="text-sm">{predefinedQuery}</div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Historique de recherche */}
        {searchHistory.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Historique récent</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {searchHistory.map((historyQuery, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="cursor-pointer hover:bg-gray-200"
                    onClick={() => {
                      setQuery(historyQuery);
                      setCurrentQuery(historyQuery);
                    }}
                  >
                    {historyQuery.length > 50 
                      ? `${historyQuery.substring(0, 50)}...` 
                      : historyQuery
                    }
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Statut */}
        {(results.length > 0 || error) && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                {error ? (
                  <>
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                    <span className="text-red-700">Erreur de recherche</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="text-green-700">
                      Recherche réussie - {results.length} résultats trouvés
                    </span>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Résultats de recherche */}
        {currentQuery && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">
              Résultats pour: "{currentQuery}"
            </h2>
            <EnhancedSearch
              query={currentQuery}
              onResults={handleResults}
              onError={handleError}
              maxResults={10}
              autoSearch={true}
            />
          </div>
        )}

        {/* Informations de debug */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Informations de debug</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div>
                <strong>Requête actuelle:</strong> {currentQuery || 'Aucune'}
              </div>
              <div>
                <strong>Nombre de résultats:</strong> {results.length}
              </div>
              <div>
                <strong>Erreur:</strong> {error ? error.message : 'Aucune'}
              </div>
              <div>
                <strong>Historique:</strong> {searchHistory.length} requêtes
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Instructions de test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-gray-600">
              <p>1. Utilisez les requêtes prédéfinies pour tester rapidement</p>
              <p>2. Entrez votre propre requête dans le champ de recherche</p>
              <p>3. Observez la gestion des erreurs et les tentatives de retry</p>
              <p>4. Vérifiez que les résultats s'affichent correctement</p>
              <p>5. Testez avec des requêtes qui pourraient échouer</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
