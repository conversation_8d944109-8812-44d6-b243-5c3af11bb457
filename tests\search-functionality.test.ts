import { test, expect } from '@playwright/test';

test.describe('Enhanced Search Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Aller à la page de test
    await page.goto('/test-search');
    await page.waitForLoadState('networkidle');
  });

  test('should load the search test page correctly', async ({ page }) => {
    // Vérifier que la page se charge
    await expect(page.locator('h1, [role="heading"]')).toContainText('Test de Recherche Avancée');
    
    // Vérifier que le champ de recherche est présent
    await expect(page.locator('input[placeholder*="recherche"]')).toBeVisible();
    
    // Vérifier que le bouton de recherche est présent
    await expect(page.locator('button:has-text("Rechercher")')).toBeVisible();
  });

  test('should display predefined queries', async ({ page }) => {
    // Vérifier que les requêtes prédéfinies sont affichées
    await expect(page.locator('text=Requêtes de test')).toBeVisible();
    
    // Vérifier qu'il y a au moins une requête prédéfinie
    const predefinedQueries = page.locator('button:has-text("innovations")');
    await expect(predefinedQueries.first()).toBeVisible();
  });

  test('should perform search with predefined query', async ({ page }) => {
    // Cliquer sur une requête prédéfinie
    await page.locator('button:has-text("Innovations en IA générative 2024")').click();
    
    // Attendre que la recherche se lance
    await page.waitForTimeout(1000);
    
    // Vérifier que la requête apparaît dans le titre des résultats
    await expect(page.locator('text=Résultats pour:')).toBeVisible();
    
    // Attendre les résultats ou une erreur (max 30 secondes)
    await page.waitForFunction(
      () => {
        const results = document.querySelector('[data-testid="search-results"]');
        const error = document.querySelector('[data-testid="search-error"]');
        const loading = document.querySelector('[data-testid="search-loading"]');
        return (results && !loading) || error;
      },
      { timeout: 30000 }
    );
  });

  test('should handle manual search input', async ({ page }) => {
    const searchQuery = 'Test de recherche automatisé';
    
    // Entrer une requête de recherche
    await page.fill('input[placeholder*="recherche"]', searchQuery);
    
    // Cliquer sur le bouton de recherche
    await page.click('button:has-text("Rechercher")');
    
    // Vérifier que la requête apparaît dans les résultats
    await expect(page.locator(`text=Résultats pour: "${searchQuery}"`)).toBeVisible();
  });

  test('should handle search with Enter key', async ({ page }) => {
    const searchQuery = 'Test avec touche Entrée';
    
    // Entrer une requête et appuyer sur Entrée
    await page.fill('input[placeholder*="recherche"]', searchQuery);
    await page.press('input[placeholder*="recherche"]', 'Enter');
    
    // Vérifier que la recherche se lance
    await expect(page.locator(`text=Résultats pour: "${searchQuery}"`)).toBeVisible();
  });

  test('should show search history', async ({ page }) => {
    // Effectuer plusieurs recherches
    const queries = ['Première recherche', 'Deuxième recherche'];
    
    for (const query of queries) {
      await page.fill('input[placeholder*="recherche"]', query);
      await page.press('input[placeholder*="recherche"]', 'Enter');
      await page.waitForTimeout(500);
    }
    
    // Vérifier que l'historique apparaît
    await expect(page.locator('text=Historique récent')).toBeVisible();
    
    // Vérifier que les requêtes apparaissent dans l'historique
    for (const query of queries) {
      await expect(page.locator(`text=${query}`)).toBeVisible();
    }
  });

  test('should handle empty search gracefully', async ({ page }) => {
    // Essayer de rechercher avec un champ vide
    await page.click('button:has-text("Rechercher")');
    
    // Le bouton devrait être désactivé ou rien ne devrait se passer
    await expect(page.locator('button:has-text("Rechercher")')).toBeDisabled();
  });

  test('should display debug information', async ({ page }) => {
    // Vérifier que les informations de debug sont présentes
    await expect(page.locator('text=Informations de debug')).toBeVisible();
    await expect(page.locator('text=Requête actuelle:')).toBeVisible();
    await expect(page.locator('text=Nombre de résultats:')).toBeVisible();
  });

  test('should show instructions', async ({ page }) => {
    // Vérifier que les instructions sont affichées
    await expect(page.locator('text=Instructions de test')).toBeVisible();
    await expect(page.locator('text=Utilisez les requêtes prédéfinies')).toBeVisible();
  });
});

test.describe('Enhanced Search API', () => {
  test('should respond to API requests', async ({ request }) => {
    // Tester l'API directement
    const response = await request.post('/api/enhanced-search', {
      data: {
        query: 'test API search',
        maxResults: 5,
        language: 'fr',
        category: 'general'
      }
    });
    
    expect(response.status()).toBeLessThan(500);
    
    const data = await response.json();
    expect(data).toHaveProperty('success');
    
    if (data.success) {
      expect(data).toHaveProperty('results');
      expect(Array.isArray(data.results)).toBeTruthy();
    } else {
      expect(data).toHaveProperty('error');
    }
  });

  test('should handle invalid requests', async ({ request }) => {
    // Tester avec des données invalides
    const response = await request.post('/api/enhanced-search', {
      data: {
        query: '', // Requête vide
        maxResults: -1, // Nombre invalide
      }
    });
    
    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data.success).toBeFalsy();
    expect(data).toHaveProperty('error');
  });

  test('should handle GET requests', async ({ request }) => {
    // Tester la méthode GET
    const response = await request.get('/api/enhanced-search?query=test');
    
    expect(response.status()).toBeLessThan(500);
    
    const data = await response.json();
    expect(data).toHaveProperty('success');
  });
});

test.describe('Error Handling', () => {
  test('should handle network errors gracefully', async ({ page }) => {
    // Simuler une erreur réseau en bloquant les requêtes API
    await page.route('/api/enhanced-search', route => {
      route.abort('failed');
    });
    
    // Effectuer une recherche
    await page.fill('input[placeholder*="recherche"]', 'Test erreur réseau');
    await page.click('button:has-text("Rechercher")');
    
    // Vérifier que l'erreur est gérée
    await expect(page.locator('text=Erreur de recherche')).toBeVisible({ timeout: 10000 });
  });

  test('should show retry option on errors', async ({ page }) => {
    // Simuler une erreur temporaire
    let requestCount = 0;
    await page.route('/api/enhanced-search', route => {
      requestCount++;
      if (requestCount === 1) {
        route.fulfill({
          status: 500,
          body: JSON.stringify({ success: false, error: 'Temporary error' })
        });
      } else {
        route.continue();
      }
    });
    
    // Effectuer une recherche
    await page.fill('input[placeholder*="recherche"]', 'Test retry');
    await page.click('button:has-text("Rechercher")');
    
    // Attendre l'erreur et vérifier le bouton retry
    await expect(page.locator('button:has-text("Réessayer")')).toBeVisible({ timeout: 10000 });
  });
});
