// Test simple pour vérifier la recherche extrême
// Utilise fetch natif de Node.js 18+

async function testExtremeSearch() {
  console.log('🚀 Test de la recherche extrême...');
  
  const testQuery = 'Fais une recherche extrême sur les dernières innovations en intelligence artificielle générative en 2024, en comparant GPT-4, <PERSON> et <PERSON>';
  
  try {
    console.log('📡 Envoi de la requête...');
    
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: testQuery,
          }
        ],
        id: 'test-chat-' + Date.now(),
        selectedChatModel: 'chat-model',
      }),
    });

    console.log(`📊 Statut de la réponse: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log('📖 Lecture du stream...');
    
    let chunkCount = 0;
    let hasExtremeSearch = false;
    let hasSteps = false;
    let hasReasoning = false;
    
    const reader = response.body.getReader();
    
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log(`✅ Stream terminé après ${chunkCount} chunks`);
        break;
      }

      chunkCount++;
      const chunk = new TextDecoder().decode(value);
      
      // Vérifier les éléments importants
      if (chunk.includes('extremeSearch')) {
        hasExtremeSearch = true;
        console.log(`🔍 ExtremeSearch détecté dans le chunk ${chunkCount}`);
      }
      
      if (chunk.includes('step') || chunk.includes('Step')) {
        hasSteps = true;
        console.log(`👣 Étapes détectées dans le chunk ${chunkCount}`);
      }
      
      if (chunk.includes('reasoning') || chunk.includes('Reasoning')) {
        hasReasoning = true;
        console.log(`🧠 Raisonnement détecté dans le chunk ${chunkCount}`);
      }
      
      // Log des chunks importants
      if (chunk.includes('status') || chunk.includes('annotation')) {
        console.log(`📦 Chunk ${chunkCount}: ${chunk.substring(0, 150)}...`);
      }
      
      // Arrêter après un nombre raisonnable de chunks pour éviter les boucles infinies
      if (chunkCount > 200) {
        console.log('⚠️ Arrêt du test après 200 chunks');
        break;
      }
    }
    
    // Résultats du test
    console.log('\n📋 Résultats du test:');
    console.log(`- Chunks reçus: ${chunkCount}`);
    console.log(`- ExtremeSearch utilisé: ${hasExtremeSearch ? '✅' : '❌'}`);
    console.log(`- Étapes détectées: ${hasSteps ? '✅' : '❌'}`);
    console.log(`- Raisonnement détecté: ${hasReasoning ? '✅' : '❌'}`);
    
    if (hasExtremeSearch && hasSteps) {
      console.log('\n🎉 Test RÉUSSI: La recherche extrême fonctionne avec les étapes!');
      return true;
    } else {
      console.log('\n❌ Test ÉCHOUÉ: Problème avec la recherche extrême');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    return false;
  }
}

// Fonction pour vérifier que le serveur est démarré
async function waitForServer(maxAttempts = 10) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch('http://localhost:3000/api/health', {
        method: 'GET',
      });
      if (response.ok) {
        console.log('✅ Serveur prêt');
        return true;
      }
    } catch (error) {
      console.log(`⏳ Tentative ${i + 1}/${maxAttempts}: Serveur non prêt, attente...`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('❌ Serveur non accessible après plusieurs tentatives');
  return false;
}

// Exécuter le test
async function main() {
  console.log('🔧 Vérification du serveur...');
  
  const serverReady = await waitForServer();
  if (!serverReady) {
    console.log('❌ Impossible de se connecter au serveur. Assurez-vous que npm run dev est lancé.');
    process.exit(1);
  }
  
  const success = await testExtremeSearch();
  process.exit(success ? 0 : 1);
}

main().catch(error => {
  console.error('💥 Erreur fatale:', error);
  process.exit(1);
});
