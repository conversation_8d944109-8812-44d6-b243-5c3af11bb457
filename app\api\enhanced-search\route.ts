import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Schema de validation pour la requête
const SearchRequestSchema = z.object({
  query: z.string().min(1, 'Query is required'),
  maxResults: z.number().min(1).max(50).default(10),
  language: z.string().default('en'),
  category: z.enum(['general', 'news', 'finance', 'research']).default('general'),
});

interface SearchResult {
  title: string;
  url: string;
  content: string;
  publishedDate?: string;
  favicon?: string;
}

interface SearchProvider {
  name: string;
  search: (query: string, options: any) => Promise<SearchResult[]>;
  priority: number;
}

// Simulateur de recherche Tavily
async function searchWithTavily(query: string, options: any): Promise<SearchResult[]> {
  try {
    const apiKey = process.env.TAVILY_API_KEY;
    if (!apiKey) {
      throw new Error('TAVILY_API_KEY not configured');
    }

    const response = await fetch('https://api.tavily.com/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        query,
        max_results: options.maxResults,
        include_domains: [],
        exclude_domains: [],
        include_answer: false,
        include_raw_content: false,
        include_images: false,
      }),
      signal: AbortSignal.timeout(25000),
    });

    if (!response.ok) {
      throw new Error(`Tavily API error: ${response.status}`);
    }

    const data = await response.json();
    return (data.results || []).map((result: any) => ({
      title: result.title || 'Untitled',
      url: result.url,
      content: result.content || '',
      publishedDate: result.published_date,
      favicon: `https://www.google.com/s2/favicons?domain=${new URL(result.url).hostname}`,
    }));
  } catch (error) {
    console.error('Tavily search error:', error);
    throw error;
  }
}

// Simulateur de recherche Exa
async function searchWithExa(query: string, options: any): Promise<SearchResult[]> {
  try {
    const apiKey = process.env.EXA_API_KEY;
    if (!apiKey) {
      throw new Error('EXA_API_KEY not configured');
    }

    const response = await fetch('https://api.exa.ai/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
      },
      body: JSON.stringify({
        query,
        numResults: options.maxResults,
        type: 'keyword',
        contents: {
          text: true,
        },
      }),
      signal: AbortSignal.timeout(25000),
    });

    if (!response.ok) {
      throw new Error(`Exa API error: ${response.status}`);
    }

    const data = await response.json();
    return (data.results || []).map((result: any) => ({
      title: result.title || 'Untitled',
      url: result.url,
      content: result.text || '',
      publishedDate: result.publishedDate,
      favicon: `https://www.google.com/s2/favicons?domain=${new URL(result.url).hostname}`,
    }));
  } catch (error) {
    console.error('Exa search error:', error);
    throw error;
  }
}

// Recherche de fallback avec des données simulées
async function searchWithFallback(query: string, options: any): Promise<SearchResult[]> {
  console.log('Using fallback search for:', query);
  
  // Données simulées basées sur la requête
  const mockResults: SearchResult[] = [
    {
      title: `Innovations en IA générative 2024 - ${query}`,
      url: 'https://example.com/ai-innovations-2024',
      content: 'Les dernières innovations en intelligence artificielle générative incluent des améliorations significatives dans les modèles de langage, la génération d\'images, et les capacités multimodales.',
      publishedDate: '2024-01-15',
      favicon: 'https://www.google.com/s2/favicons?domain=example.com',
    },
    {
      title: 'Comparaison GPT-4 vs Claude vs Gemini 2024',
      url: 'https://example.com/llm-comparison-2024',
      content: 'Une analyse détaillée des performances, capacités et innovations des principaux modèles de langage en 2024.',
      publishedDate: '2024-02-01',
      favicon: 'https://www.google.com/s2/favicons?domain=example.com',
    },
    {
      title: 'Tendances IA générative - Rapport 2024',
      url: 'https://example.com/ai-trends-2024',
      content: 'Les tendances émergentes en IA générative, incluant les nouvelles architectures, les applications industrielles et les défis éthiques.',
      publishedDate: '2024-01-30',
      favicon: 'https://www.google.com/s2/favicons?domain=example.com',
    },
  ];

  // Simuler un délai de recherche
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return mockResults.slice(0, options.maxResults);
}

// Configuration des fournisseurs de recherche
const searchProviders: SearchProvider[] = [
  { name: 'tavily', search: searchWithTavily, priority: 1 },
  { name: 'exa', search: searchWithExa, priority: 2 },
  { name: 'fallback', search: searchWithFallback, priority: 3 },
];

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, maxResults, language, category } = SearchRequestSchema.parse(body);

    console.log(`Enhanced Search API: Searching for "${query}"`);

    const searchOptions = { maxResults, language, category };
    let lastError: Error | null = null;
    let results: SearchResult[] = [];

    // Essayer chaque fournisseur dans l'ordre de priorité
    for (const provider of searchProviders.sort((a, b) => a.priority - b.priority)) {
      try {
        console.log(`Trying search provider: ${provider.name}`);
        results = await provider.search(query, searchOptions);
        
        if (results.length > 0) {
          console.log(`Found ${results.length} results from ${provider.name}`);
          break;
        }
      } catch (error) {
        console.warn(`Provider ${provider.name} failed:`, error);
        lastError = error as Error;
        continue;
      }
    }

    // Si aucun résultat n'a été trouvé, retourner une erreur
    if (results.length === 0) {
      throw lastError || new Error('No search results found from any provider');
    }

    return NextResponse.json({
      success: true,
      results,
      query,
      provider: searchProviders.find(p => results.length > 0)?.name || 'unknown',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Enhanced Search API Error:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const isValidationError = error instanceof z.ZodError;

    return NextResponse.json(
      {
        success: false,
        error: isValidationError ? 'Invalid request parameters' : errorMessage,
        details: isValidationError ? error.errors : undefined,
        timestamp: new Date().toISOString(),
      },
      { 
        status: isValidationError ? 400 : 500 
      }
    );
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query');

  if (!query) {
    return NextResponse.json(
      { error: 'Query parameter is required' },
      { status: 400 }
    );
  }

  // Rediriger vers POST avec les paramètres par défaut
  return POST(new NextRequest(request.url, {
    method: 'POST',
    body: JSON.stringify({
      query,
      maxResults: 10,
      language: 'fr',
      category: 'general',
    }),
    headers: {
      'Content-Type': 'application/json',
    },
  }));
}
